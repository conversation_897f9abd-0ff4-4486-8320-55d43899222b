"""
自定义测试文件 - 支持灵活参数传入
"""
import requests
import time
from typing import List, Dict, Optional

BASE_URL = "http://localhost:8000"

def download_images(task_id: str, 
                   jimeng: Optional[List[Dict[str, str]]] = None,
                   wanxiang: Optional[List[Dict[str, str]]] = None, 
                   doubao: Optional[List[Dict[str, str]]] = None) -> str:
    # 构建请求数据
    data_list = []
    
    # 添加即梦数据
    if jimeng:
        data_list.append({
            "model_name": "即梦",
            "data": jimeng
        })
    
    # 添加万相数据
    if wanxiang:
        data_list.append({
            "model_name": "万相", 
            "data": wanxiang
        })
    
    # 添加豆包数据
    if doubao:
        data_list.append({
            "model_name": "豆包",
            "data": doubao
        })
    
    # 检查是否有数据
    if not data_list:
        raise ValueError("至少需要传入一个模型的数据（jimeng、wanxiang、doubao）")
    
    # 构建完整请求体
    request_data = {
        "task_id": task_id,
        "data": data_list
    }
    
    try:
        # 发送下载请求
        response = requests.post(f"{BASE_URL}/download", json=request_data)
        response.raise_for_status()

        result = response.json()
        print(f"✅ 下载请求成功: {result['message']}")

        # 等待下载完成
        print("⏳ 等待图片下载完成...")
        time.sleep(5)  # 可以根据图片数量调整等待时间

        # 返回导出链接
        export_url = f"{BASE_URL}/export/{task_id}"
        
        return export_url
        
    except requests.exceptions.RequestException as e:
        raise Exception(f"下载请求失败: {e}")

def test_download_and_export(export_url: str, filename: str = None) -> bool:
    """
    测试导出功能
    
    Args:
        export_url: 导出链接
        filename: 保存的文件名，默认为 test_export.zip
    
    Returns:
        bool: 是否导出成功
    """
    if not filename:
        filename = "test_export.zip"
    
    try:
        print(f"📦 开始导出ZIP文件...")
        response = requests.get(export_url)
        response.raise_for_status()
        
        with open(filename, 'wb') as f:
            f.write(response.content)
        
        print(f"✅ 导出成功: {filename}")
        print(f"📏 文件大小: {len(response.content)} bytes")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 导出失败: {e}")
        return False

# 测试示例
def example_1():
    """示例1: 只使用即梦模型"""
    print("=" * 50)
    print("🎯 示例1: 只使用即梦模型")
    
    jimeng_data = [
        {"prompt": "山水画", "url": "https://picsum.photos/400/400?random=1"},
        {"prompt": "人物肖像", "url": "https://picsum.photos/400/400?random=2"}
    ]
    
    try:
        export_url = download_images(
            task_id="test-jimeng-only",
            jimeng=jimeng_data
        )
        test_download_and_export(export_url, "jimeng_only.zip")
    except Exception as e:
        print(f"❌ 示例1失败: {e}")

def example_2():
    """示例2: 使用多个模型"""
    print("=" * 50)
    print("🎯 示例2: 使用多个模型")
    
    jimeng_data = [
        {"prompt": "风景画", "url": "https://picsum.photos/400/400?random=10"}
    ]
    
    wanxiang_data = [
        {"prompt": "抽象艺术", "url": "https://picsum.photos/400/400?random=11"},
        {"prompt": "现代设计", "url": "https://picsum.photos/400/400?random=12"}
    ]
    
    doubao_data = [
        {"prompt": "蓝天白云", "url": "https://picsum.photos/400/400?random=13"}
    ]
    
    try:
        export_url = download_images(
            task_id="test-multi-models",
            jimeng=jimeng_data,
            wanxiang=wanxiang_data,
            doubao=doubao_data
        )
        test_download_and_export(export_url, "multi_models.zip")
    except Exception as e:
        print(f"❌ 示例2失败: {e}")

def example_3():
    """示例3: 使用真实豆包数据"""
    print("=" * 50)
    print("🎯 示例3: 使用真实豆包数据")
    
    doubao_data = [
        {
            "prompt": "蓝天白云",
            "url": "https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/021756448712889eb49daf9acf049b217c8fde4b868ba5b56f462.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250829%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250829T062515Z&X-Tos-Expires=86400&X-Tos-Signature=3c09732c440bc7a20c4d48c3a567f3231202297110d31463ab4fb2dff79c9533&X-Tos-SignedHeaders=host"
        },
        {
            "prompt": "小孩在公园玩耍",
            "url": "https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/021756448715443b8c9d687c083be4564d37c31fb389ac321a6bc.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250829%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250829T062517Z&X-Tos-Expires=86400&X-Tos-Signature=7efedef7f85cdea8fe0f6f5b64840902a52bc1ee42f3bdf3bfbf070fb8d431cd&X-Tos-SignedHeaders=host"
        }
    ]
    
    try:
        export_url = download_images(
            task_id="485c505116fd47f0b4a548a6c60792a1",
            doubao=doubao_data
        )
        test_download_and_export(export_url, "doubao_real.zip")
    except Exception as e:
        print(f"❌ 示例3失败: {e}")

def custom_test():
    """自定义测试 - 可以在这里修改参数"""
    print("=" * 50)
    print("🎯 自定义测试")
    
    # 在这里修改你的测试数据
    task_id = "my-custom-test"
    
    jimeng_data = [
        {"prompt": "测试图片1", "url": "https://picsum.photos/300/300?random=100"}
    ]
    
    # wanxiang_data = [
    #     {"prompt": "测试图片2", "url": "https://picsum.photos/300/300?random=101"}
    # ]
    
    # doubao_data = [
    #     {"prompt": "测试图片3", "url": "https://picsum.photos/300/300?random=102"}
    # ]
    
    try:
        export_url = download_images(
            task_id=task_id,
            jimeng=jimeng_data,
            # wanxiang=wanxiang_data,  # 取消注释来启用
            # doubao=doubao_data       # 取消注释来启用
        )
        test_download_and_export(export_url, "custom_test.zip")
    except Exception as e:
        print(f"❌ 自定义测试失败: {e}")

def main():
    """主函数 - 运行所有示例"""
    print("🚀 开始自定义参数测试...")
    
    # 检查服务器
    try:
        response = requests.get(BASE_URL, timeout=5)
        if response.status_code != 200:
            print("❌ 服务器未正常运行")
            return
    except:
        print("❌ 无法连接到服务器，请确保服务已启动: python app.py")
        return
    
    print("✅ 服务器连接正常\n")
    
    # 运行示例
    example_1()
    example_2() 
    example_3()
    custom_test()
    
    print("\n🎉 所有测试完成！")

if __name__ == "__main__":
    main()
