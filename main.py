"""
图片生成任务管理系统主入口
"""
import uvicorn
import os
from dotenv import load_dotenv
from models import create_tables

def main():
    """启动应用"""
    load_dotenv()

    # 创建数据库表
    print("正在初始化数据库...")
    create_tables()
    print("数据库初始化完成")

    # 启动FastAPI应用
    host = os.getenv("API_HOST", "0.0.0.0")
    port = int(os.getenv("API_PORT", "8000"))

    print(f"正在启动服务器 http://{host}:{port}")
    uvicorn.run(
        "app:app",
        host=host,
        port=port,
        reload=True
    )

if __name__ == "__main__":
    main()
