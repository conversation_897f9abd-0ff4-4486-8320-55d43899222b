from sqlalchemy import Column, Integer, String, DateTime, Text, create_engine
from sqlalchemy.orm import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
import os
from dotenv import load_dotenv

load_dotenv()

Base = declarative_base()


class ImageTask(Base):
    __tablename__ = "image_tasks"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    task_id = Column(String(255), nullable=False, index=True)
    model_name = Column(String(100), nullable=False)
    prompt = Column(Text, nullable=False)
    image_url = Column(Text, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)


# 数据库连接配置
MYSQL_HOST = os.getenv("MYSQL_HOST", "localhost")
MYSQL_PORT = os.getenv("MYSQL_PORT", "3306")
MYSQL_USER = os.getenv("MYSQL_USER", "root")
MYSQL_PASSWORD = os.getenv("MYSQL_PASSWORD", "")
MYSQL_DATABASE = os.getenv("MYSQL_DATABASE", "image_tasks")

DATABASE_URL = f"mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DATABASE}"

engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def cleanup_old_data():
    """清理超过24小时的数据"""
    from datetime import datetime, timedelta
    import shutil
    from pathlib import Path

    # 计算24小时前的时间
    cutoff_time = datetime.utcnow() - timedelta(hours=24)

    db = SessionLocal()
    try:
        # 查找超过24小时的任务
        old_tasks = db.query(ImageTask).filter(ImageTask.created_at < cutoff_time).all()

        # 获取要删除的task_id列表
        task_ids_to_delete = list(set(task.task_id for task in old_tasks))

        print(f"发现 {len(task_ids_to_delete)} 个超过24小时的任务需要清理")

        # 删除本地文件夹
        images_base_path = Path(os.getenv("IMAGES_BASE_PATH", "./downloads"))
        exports_base_path = Path(os.getenv("EXPORT_BASE_PATH", "./exports"))

        deleted_folders = 0
        deleted_zips = 0

        for task_id in task_ids_to_delete:
            # 删除下载文件夹
            task_folder = images_base_path / task_id
            if task_folder.exists():
                shutil.rmtree(task_folder)
                deleted_folders += 1
                print(f"删除文件夹: {task_folder}")

            # 删除ZIP文件
            zip_file = exports_base_path / f"{task_id}.zip"
            if zip_file.exists():
                zip_file.unlink()
                deleted_zips += 1
                print(f"删除ZIP文件: {zip_file}")

        # 删除数据库记录
        deleted_records = db.query(ImageTask).filter(ImageTask.created_at < cutoff_time).delete()
        db.commit()

        print(f"清理完成: 删除了 {deleted_records} 条数据库记录, {deleted_folders} 个文件夹, {deleted_zips} 个ZIP文件")

        return {
            "deleted_records": deleted_records,
            "deleted_folders": deleted_folders,
            "deleted_zips": deleted_zips,
            "cutoff_time": cutoff_time.isoformat()
        }

    except Exception as e:
        db.rollback()
        print(f"清理数据时出错: {e}")
        raise e
    finally:
        db.close()
