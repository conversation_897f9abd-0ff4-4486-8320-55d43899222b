"""
测试客户端 - 用于测试API接口
"""
import requests
import json
import time

# API基础URL
BASE_URL = "http://localhost:8000"

def test_create_task():
    """测试创建任务"""
    print("=== 测试创建任务 ===")
    
    # 测试数据
    test_data = {
        "data": [
            {
                "model_name": "即梦",
                "data": [
                    {
                        "prompt": "一只可爱的小猫咪在花园里玩耍",
                        "url": "https://picsum.photos/512/512?random=1"
                    },
                    {
                        "prompt": "美丽的日落风景画",
                        "url": "https://picsum.photos/512/512?random=2"
                    }
                ]
            },
            {
                "model_name": "万相",
                "data": [
                    {
                        "prompt": "科幻城市夜景",
                        "url": "https://picsum.photos/512/512?random=3"
                    }
                ]
            },
            {
                "model_name": "豆包",
                "data": [
                    {
                        "prompt": "抽象艺术作品",
                        "url": "https://picsum.photos/512/512?random=4"
                    }
                ]
            }
        ]
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/tasks", json=test_data)
        response.raise_for_status()
        
        result = response.json()
        print(f"任务创建成功: {result}")
        return result["task_id"]
        
    except requests.exceptions.RequestException as e:
        print(f"创建任务失败: {e}")
        return None

def test_get_task_info(task_id):
    """测试获取任务信息"""
    print(f"\n=== 测试获取任务信息: {task_id} ===")
    
    try:
        response = requests.get(f"{BASE_URL}/api/tasks/{task_id}")
        response.raise_for_status()
        
        result = response.json()
        print(f"任务信息: {json.dumps(result, indent=2, ensure_ascii=False)}")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"获取任务信息失败: {e}")
        return False

def test_download_images(task_id):
    """测试下载图片"""
    print(f"\n=== 测试下载图片: {task_id} ===")
    
    try:
        response = requests.post(f"{BASE_URL}/api/download/{task_id}")
        response.raise_for_status()
        
        result = response.json()
        print(f"下载结果: {result}")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"下载图片失败: {e}")
        return False

def test_export_task(task_id):
    """测试导出任务"""
    print(f"\n=== 测试导出任务: {task_id} ===")
    
    try:
        response = requests.get(f"{BASE_URL}/api/export/{task_id}")
        response.raise_for_status()
        
        # 保存ZIP文件
        filename = f"exported_task_{task_id}.zip"
        with open(filename, 'wb') as f:
            f.write(response.content)
        
        print(f"导出成功，文件保存为: {filename}")
        print(f"文件大小: {len(response.content)} bytes")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"导出任务失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试图片生成任务管理系统...")
    
    # 测试根路径
    try:
        response = requests.get(BASE_URL)
        print(f"API状态: {response.json()}")
    except:
        print("无法连接到API服务器，请确保服务器已启动")
        return
    
    # 创建任务
    task_id = test_create_task()
    if not task_id:
        return
    
    # 等待一段时间让后台下载完成
    print("\n等待图片下载完成...")
    time.sleep(5)
    
    # 获取任务信息
    test_get_task_info(task_id)
    
    # 手动触发下载（如果需要）
    test_download_images(task_id)
    
    # 等待下载完成
    time.sleep(3)
    
    # 导出任务
    test_export_task(task_id)
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
