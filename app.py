from fastapi import <PERSON><PERSON><PERSON>, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from sqlalchemy import Column, Integer, String, DateTime, Text, create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from datetime import datetime
import os
import requests
import zipfile
import uuid
from pathlib import Path
from urllib.parse import urlparse
from dotenv import load_dotenv
import uvicorn

load_dotenv()

# 数据库配置
MYSQL_HOST = os.getenv("MYSQL_HOST", "localhost")
MYSQL_PORT = os.getenv("MYSQL_PORT", "3306")
MYSQL_USER = os.getenv("MYSQL_USER", "root")
MYSQL_PASSWORD = os.getenv("MYSQL_PASSWORD", "")
MYSQL_DATABASE = os.getenv("MYSQL_DATABASE", "image_tasks")

DATABASE_URL = f"mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DATABASE}"

# 数据库模型
Base = declarative_base()

class ImageTask(Base):
    __tablename__ = "image_tasks"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    task_id = Column(String(255), nullable=False, index=True)
    model_name = Column(String(100), nullable=False)
    prompt = Column(Text, nullable=False)
    image_url = Column(Text, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)

# 数据库连接
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 创建表
Base.metadata.create_all(bind=engine)

# 文件路径配置
IMAGES_BASE_PATH = Path(os.getenv("IMAGES_BASE_PATH", "./downloads"))
EXPORT_BASE_PATH = Path(os.getenv("EXPORT_BASE_PATH", "./exports"))
IMAGES_BASE_PATH.mkdir(exist_ok=True)
EXPORT_BASE_PATH.mkdir(exist_ok=True)

app = FastAPI(title="图片生成任务管理系统", version="1.0.0")

# Pydantic模型
class ImageData(BaseModel):
    prompt: str
    url: str

class ModelData(BaseModel):
    model_name: str
    data: List[ImageData]

class TaskRequest(BaseModel):
    data: List[ModelData]
    task_id: Optional[str] = None

class TaskResponse(BaseModel):
    task_id: str
    message: str

# 服务函数
def save_image_data(data: List[Dict[str, Any]], task_id: str = None) -> str:
    """保存图片数据到数据库"""
    if not task_id:
        task_id = str(uuid.uuid4())

    db = next(get_db())
    try:
        for model_data in data:
            model_name = model_data["model_name"]
            for item in model_data["data"]:
                image_task = ImageTask(
                    task_id=task_id,
                    model_name=model_name,
                    prompt=item["prompt"],
                    image_url=item["url"]
                )
                db.add(image_task)
        db.commit()
        return task_id
    except Exception as e:
        db.rollback()
        raise e
    finally:
        db.close()

def download_images_for_task(task_id: str) -> bool:
    """下载指定任务的所有图片到本地"""
    db = next(get_db())
    try:
        image_tasks = db.query(ImageTask).filter(ImageTask.task_id == task_id).all()
        if not image_tasks:
            return False

        task_folder = IMAGES_BASE_PATH / task_id
        task_folder.mkdir(exist_ok=True)

        for image_task in image_tasks:
            model_folder = task_folder / image_task.model_name
            model_folder.mkdir(exist_ok=True)

            # 生成文件名（提示词前10个字符）
            safe_prompt = "".join(c for c in image_task.prompt[:10] if c.isalnum() or c in (' ', '-', '_')).rstrip()
            if not safe_prompt:
                safe_prompt = f"image_{image_task.id}"

            parsed_url = urlparse(image_task.image_url)
            file_extension = os.path.splitext(parsed_url.path)[1] or '.jpg'
            filename = f"{safe_prompt}{file_extension}"
            file_path = model_folder / filename

            try:
                response = requests.get(image_task.image_url, timeout=30)
                response.raise_for_status()
                with open(file_path, 'wb') as f:
                    f.write(response.content)
            except Exception as e:
                print(f"下载图片失败: {image_task.image_url}, 错误: {e}")
                continue

        return True
    except Exception as e:
        print(f"下载任务图片失败: {e}")
        return False
    finally:
        db.close()

def create_task_zip(task_id: str) -> str:
    """将任务文件夹打包为ZIP文件"""
    task_folder = IMAGES_BASE_PATH / task_id
    if not task_folder.exists():
        raise FileNotFoundError(f"任务文件夹不存在: {task_id}")

    zip_filename = f"{task_id}.zip"
    zip_path = EXPORT_BASE_PATH / zip_filename

    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in task_folder.rglob('*'):
            if file_path.is_file():
                arcname = file_path.relative_to(task_folder)
                zipf.write(file_path, arcname)

    return str(zip_path)

def get_task_info(task_id: str) -> Dict[str, Any]:
    """获取任务信息"""
    db = next(get_db())
    try:
        image_tasks = db.query(ImageTask).filter(ImageTask.task_id == task_id).all()
        if not image_tasks:
            return {}

        models_info = {}
        for task in image_tasks:
            if task.model_name not in models_info:
                models_info[task.model_name] = []
            models_info[task.model_name].append({
                "prompt": task.prompt,
                "image_url": task.image_url,
                "created_at": task.created_at.isoformat()
            })

        return {
            "task_id": task_id,
            "total_images": len(image_tasks),
            "models": models_info,
            "created_at": image_tasks[0].created_at.isoformat() if image_tasks else None
        }
    finally:
        db.close()

# API路由
@app.post("/api/tasks", response_model=TaskResponse)
async def create_task(request: TaskRequest, background_tasks: BackgroundTasks):
    """创建新的图片生成任务"""
    try:
        data_list = []
        for model_data in request.data:
            data_list.append({
                "model_name": model_data.model_name,
                "data": [{"prompt": item.prompt, "url": item.url} for item in model_data.data]
            })

        task_id = save_image_data(data_list, request.task_id)
        background_tasks.add_task(download_images_for_task, task_id)

        return TaskResponse(task_id=task_id, message="任务创建成功，图片正在后台下载")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建任务失败: {str(e)}")

@app.get("/api/tasks/{task_id}")
async def get_task(task_id: str):
    """获取任务信息"""
    try:
        task_info = get_task_info(task_id)
        if not task_info:
            raise HTTPException(status_code=404, detail="任务不存在")
        return task_info
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务信息失败: {str(e)}")

@app.get("/api/export/{task_id}")
async def export_task(task_id: str):
    """导出任务的所有图片为ZIP文件"""
    try:
        task_info = get_task_info(task_id)
        if not task_info:
            raise HTTPException(status_code=404, detail="任务不存在")

        task_folder = IMAGES_BASE_PATH / task_id
        if not task_folder.exists():
            success = download_images_for_task(task_id)
            if not success:
                raise HTTPException(status_code=500, detail="图片下载失败")

        zip_path = create_task_zip(task_id)
        return FileResponse(path=zip_path, filename=f"task_{task_id}.zip", media_type="application/zip")
    except FileNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")

@app.post("/api/download/{task_id}")
async def download_task_images(task_id: str):
    """手动触发下载任务图片"""
    try:
        task_info = get_task_info(task_id)
        if not task_info:
            raise HTTPException(status_code=404, detail="任务不存在")

        success = download_images_for_task(task_id)
        if success:
            return {"message": "图片下载完成"}
        else:
            raise HTTPException(status_code=500, detail="图片下载失败")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"下载失败: {str(e)}")

@app.get("/")
async def root():
    """根路径，返回API信息"""
    return {
        "message": "图片生成任务管理系统",
        "version": "1.0.0",
        "endpoints": {
            "创建任务": "POST /api/tasks",
            "获取任务信息": "GET /api/tasks/{task_id}",
            "导出任务": "GET /api/export/{task_id}",
            "下载图片": "POST /api/download/{task_id}"
        }
    }

if __name__ == "__main__":
    host = os.getenv("API_HOST", "0.0.0.0")
    port = int(os.getenv("API_PORT", "8000"))
    print(f"启动服务器: http://{host}:{port}")
    uvicorn.run("app:app", host=host, port=port, reload=True)
