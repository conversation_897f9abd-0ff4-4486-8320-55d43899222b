from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse
from pydantic import BaseModel
from typing import List, Optional
import os
import uvicorn
from dotenv import load_dotenv
from models import create_tables
from image_service import ImageService

load_dotenv()

app = FastAPI(title="图片生成任务管理系统", version="1.0.0")

# 初始化服务
image_service = ImageService()

# 创建数据库表
create_tables()

# Pydantic模型
class ImageData(BaseModel):
    prompt: str
    url: str

class ModelData(BaseModel):
    model_name: str
    data: List[ImageData]

class TaskRequest(BaseModel):
    data: List[ModelData]
    task_id: Optional[str] = None

class TaskResponse(BaseModel):
    task_id: str
    message: str

# API路由
@app.post("/api/tasks", response_model=TaskResponse)
async def create_task(request: TaskRequest, background_tasks: BackgroundTasks):
    """创建新的图片生成任务"""
    try:
        data_list = []
        for model_data in request.data:
            data_list.append({
                "model_name": model_data.model_name,
                "data": [{"prompt": item.prompt, "url": item.url} for item in model_data.data]
            })

        task_id = image_service.save_image_data(data_list, request.task_id)
        background_tasks.add_task(image_service.download_images_for_task, task_id)

        return TaskResponse(task_id=task_id, message="任务创建成功，图片正在后台下载")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建任务失败: {str(e)}")

@app.get("/api/tasks/{task_id}")
async def get_task(task_id: str):
    """获取任务信息"""
    try:
        task_info = image_service.get_task_info(task_id)
        if not task_info:
            raise HTTPException(status_code=404, detail="任务不存在")
        return task_info
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务信息失败: {str(e)}")

@app.get("/api/export/{task_id}")
async def export_task(task_id: str):
    """导出任务的所有图片为ZIP文件"""
    try:
        task_info = image_service.get_task_info(task_id)
        if not task_info:
            raise HTTPException(status_code=404, detail="任务不存在")

        task_folder = image_service.images_base_path / task_id
        if not task_folder.exists():
            success = image_service.download_images_for_task(task_id)
            if not success:
                raise HTTPException(status_code=500, detail="图片下载失败")

        zip_path = image_service.create_task_zip(task_id)
        return FileResponse(path=zip_path, filename=f"task_{task_id}.zip", media_type="application/zip")
    except FileNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")

@app.post("/api/download/{task_id}")
async def download_task_images(task_id: str):
    """手动触发下载任务图片"""
    try:
        task_info = image_service.get_task_info(task_id)
        if not task_info:
            raise HTTPException(status_code=404, detail="任务不存在")

        success = image_service.download_images_for_task(task_id)
        if success:
            return {"message": "图片下载完成"}
        else:
            raise HTTPException(status_code=500, detail="图片下载失败")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"下载失败: {str(e)}")

@app.get("/")
async def root():
    """根路径，返回API信息"""
    return {
        "message": "图片生成任务管理系统",
        "version": "1.0.0",
        "endpoints": {
            "创建任务": "POST /api/tasks",
            "获取任务信息": "GET /api/tasks/{task_id}",
            "导出任务": "GET /api/export/{task_id}",
            "下载图片": "POST /api/download/{task_id}"
        }
    }

if __name__ == "__main__":
    host = os.getenv("API_HOST", "0.0.0.0")
    port = int(os.getenv("API_PORT", "8000"))
    print(f"启动服务器: http://{host}:{port}")
    uvicorn.run("app:app", host=host, port=port, reload=True)
