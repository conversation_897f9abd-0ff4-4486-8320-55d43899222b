from fastapi import Fast<PERSON><PERSON>, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse
from pydantic import BaseModel
from typing import List, Optional
import os
import uvicorn
from dotenv import load_dotenv
from models import create_tables
from image_service import ImageService

load_dotenv()

app = FastAPI(title="图片生成任务管理系统", version="1.0.0")

# 初始化服务
image_service = ImageService()

# 创建数据库表
create_tables()

# Pydantic模型
class ImageData(BaseModel):
    prompt: str
    url: str

class ModelData(BaseModel):
    model_name: str
    data: List[ImageData]

class TaskRequest(BaseModel):
    data: List[ModelData]
    task_id: Optional[str] = None



# API路由
@app.post("/export")
async def export_images(request: TaskRequest):
    """传入图片数据，直接返回ZIP文件"""
    try:
        # 转换数据格式
        data_list = []
        for model_data in request.data:
            data_list.append({
                "model_name": model_data.model_name,
                "data": [{"prompt": item.prompt, "url": item.url} for item in model_data.data]
            })

        # 保存到数据库
        task_id = image_service.save_image_data(data_list, request.task_id)

        # 下载图片
        success = image_service.download_images_for_task(task_id)
        if not success:
            raise HTTPException(status_code=500, detail="图片下载失败")

        # 创建ZIP文件
        zip_path = image_service.create_task_zip(task_id)

        # 返回ZIP文件
        return FileResponse(
            path=zip_path,
            filename=f"images_{task_id}.zip",
            media_type="application/zip"
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理失败: {str(e)}")

@app.get("/")
async def root():
    """根路径，返回API信息"""
    return {
        "message": "图片生成任务管理系统",
        "version": "1.0.0",
        "endpoint": "POST /export - 传入图片数据，返回ZIP文件"
    }

if __name__ == "__main__":
    host = os.getenv("API_HOST", "0.0.0.0")
    port = int(os.getenv("API_PORT", "8000"))
    print(f"启动服务器: http://{host}:{port}")
    uvicorn.run("app:app", host=host, port=port, reload=True)
