from fastapi import Fast<PERSON><PERSON>, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse
from pydantic import BaseModel
from typing import List, Optional
import os
import uvicorn
from dotenv import load_dotenv
from models import  cleanup_old_data
from image_service import ImageService
from cleanup_service import cleanup_service

load_dotenv()

app = FastAPI(title="图片生成任务管理系统", version="1.0.0")

# 初始化服务
image_service = ImageService()

# 启动清理服务
cleanup_service.start_scheduler()


# Pydantic模型
class ImageData(BaseModel):
    prompt: str
    url: str


class ModelData(BaseModel):
    model_name: str
    data: List[ImageData]


class TaskRequest(BaseModel):
    data: List[ModelData]
    task_id: Optional[str] = None


# API路由
@app.post("/download")
async def download_images(request: TaskRequest):
    """下载图片并创建文件夹"""
    try:
        # 转换数据格式
        data_list = []
        for model_data in request.data:
            data_list.append({
                "model_name": model_data.model_name,
                "data": [{"prompt": item.prompt, "url": item.url} for item in model_data.data]
            })

        # 保存到数据库
        task_id = image_service.save_image_data(data_list, request.task_id)

        # 下载图片
        success = image_service.download_images_for_task(task_id)
        if not success:
            raise HTTPException(status_code=500, detail="图片下载失败")

        return {
            "task_id": task_id,
            "message": "图片下载完成",
            "folder_path": str(image_service.images_base_path / task_id)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"下载失败: {str(e)}")


@app.get("/export/{task_id}")
async def export_zip(task_id: str):
    """导出指定任务的ZIP文件"""
    try:
        # 检查任务文件夹是否存在
        task_folder = image_service.images_base_path / task_id
        if not task_folder.exists():
            raise HTTPException(status_code=404, detail="任务文件夹不存在，请先下载图片")

        # 创建ZIP文件
        zip_path = image_service.create_task_zip(task_id)

        # 返回ZIP文件
        return FileResponse(
            path=zip_path,
            filename=f"images_{task_id}.zip",
            media_type="application/zip"
        )

    except FileNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")


@app.post("/cleanup")
async def manual_cleanup():
    """手动清理超过24小时的数据"""
    try:
        result = cleanup_old_data()
        return {
            "message": "清理完成",
            "result": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清理失败: {str(e)}")

@app.get("/")
async def root():
    """根路径，返回API信息"""
    return {
        "message": "图片生成任务管理系统",
        "version": "1.0.0",
        "endpoints": {
            "下载图片": "POST /download - 传入图片数据，下载并创建文件夹",
            "导出ZIP": "GET /export/{task_id} - 将指定任务文件夹打包为ZIP",
            "手动清理": "POST /cleanup - 手动清理超过24小时的数据"
        },
        "auto_cleanup": "每天23:00自动清理超过24小时的数据"
    }


if __name__ == "__main__":
    host = os.getenv("API_HOST", "0.0.0.0")
    port = int(os.getenv("API_PORT", "8000"))
    print(f"启动服务器: http://{host}:{port}")
    uvicorn.run("app:app", host=host, port=port, reload=True)
