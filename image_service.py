import os
import requests
import zipfile
import shutil
from pathlib import Path
from typing import List, Dict, Any
from sqlalchemy.orm import Session
from models import ImageTask, get_db
import uuid
from urllib.parse import urlparse
from dotenv import load_dotenv

load_dotenv()

class ImageService:
    def __init__(self):
        self.images_base_path = Path(os.getenv("IMAGES_BASE_PATH", "./downloads"))
        self.export_base_path = Path(os.getenv("EXPORT_BASE_PATH", "./exports"))
        
        # 确保目录存在
        self.images_base_path.mkdir(exist_ok=True)
        self.export_base_path.mkdir(exist_ok=True)
    
    def save_image_data(self, data: List[Dict[str, Any]], task_id: str = None) -> str:
        """
        保存图片数据到数据库
        
        Args:
            data: 图片数据列表，格式如下：
                [
                    {
                        "model_name": "xx",
                        "data": [
                            {
                                "prompt": "xx",
                                "url": "xxx"
                            }
                        ]
                    }
                ]
            task_id: 任务ID，如果不提供则自动生成
        
        Returns:
            str: 任务ID
        """
        if not task_id:
            task_id = str(uuid.uuid4())
        
        db = next(get_db())
        try:
            for model_data in data:
                model_name = model_data["model_name"]
                for item in model_data["data"]:
                    image_task = ImageTask(
                        task_id=task_id,
                        model_name=model_name,
                        prompt=item["prompt"],
                        image_url=item["url"]
                    )
                    db.add(image_task)
            
            db.commit()
            return task_id
        except Exception as e:
            db.rollback()
            raise e
        finally:
            db.close()
    
    def download_images_for_task(self, task_id: str) -> bool:
        """
        下载指定任务的所有图片到本地
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功
        """
        db = next(get_db())
        try:
            # 获取任务的所有图片记录
            image_tasks = db.query(ImageTask).filter(ImageTask.task_id == task_id).all()
            
            if not image_tasks:
                return False
            
            # 创建任务文件夹
            task_folder = self.images_base_path / task_id
            task_folder.mkdir(exist_ok=True)
            
            for image_task in image_tasks:
                # 创建模型文件夹
                model_folder = task_folder / image_task.model_name
                model_folder.mkdir(exist_ok=True)
                
                # 生成文件名（提示词前10个字符）
                safe_prompt = "".join(c for c in image_task.prompt[:10] if c.isalnum() or c in (' ', '-', '_')).rstrip()
                if not safe_prompt:
                    safe_prompt = f"image_{image_task.id}"
                
                # 获取文件扩展名
                parsed_url = urlparse(image_task.image_url)
                file_extension = os.path.splitext(parsed_url.path)[1] or '.jpg'
                
                filename = f"{safe_prompt}{file_extension}"
                file_path = model_folder / filename
                
                # 下载图片
                try:
                    response = requests.get(image_task.image_url, timeout=30)
                    response.raise_for_status()
                    
                    with open(file_path, 'wb') as f:
                        f.write(response.content)
                        
                except Exception as e:
                    print(f"下载图片失败: {image_task.image_url}, 错误: {e}")
                    continue
            
            return True
            
        except Exception as e:
            print(f"下载任务图片失败: {e}")
            return False
        finally:
            db.close()
    
    def create_task_zip(self, task_id: str) -> str:
        """
        将任务文件夹打包为ZIP文件
        
        Args:
            task_id: 任务ID
            
        Returns:
            str: ZIP文件路径
        """
        task_folder = self.images_base_path / task_id
        if not task_folder.exists():
            raise FileNotFoundError(f"任务文件夹不存在: {task_id}")
        
        zip_filename = f"{task_id}.zip"
        zip_path = self.export_base_path / zip_filename
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_path in task_folder.rglob('*'):
                if file_path.is_file():
                    # 计算相对路径
                    arcname = file_path.relative_to(task_folder)
                    zipf.write(file_path, arcname)
        
        return str(zip_path)
    
    def get_task_info(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict: 任务信息
        """
        db = next(get_db())
        try:
            image_tasks = db.query(ImageTask).filter(ImageTask.task_id == task_id).all()
            
            if not image_tasks:
                return {}
            
            # 按模型分组统计
            models_info = {}
            for task in image_tasks:
                if task.model_name not in models_info:
                    models_info[task.model_name] = []
                models_info[task.model_name].append({
                    "prompt": task.prompt,
                    "image_url": task.image_url,
                    "created_at": task.created_at.isoformat()
                })
            
            return {
                "task_id": task_id,
                "total_images": len(image_tasks),
                "models": models_info,
                "created_at": image_tasks[0].created_at.isoformat() if image_tasks else None
            }
            
        finally:
            db.close()
