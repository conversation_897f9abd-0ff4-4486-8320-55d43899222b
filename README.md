# 图片生成任务管理系统

这是一个用于管理多个AI图片生成模型任务的系统，支持图片下载、存储和批量导出功能。

## 功能特性

- 支持多个生图模型（即梦、万相、豆包等）
- 自动下载并本地存储图片
- 按任务ID和模型名称组织文件结构
- 支持任务打包导出为ZIP文件
- 提供RESTful API接口
- 使用MySQL数据库存储任务信息

## 项目结构

```
save_images/
├── app.py              # FastAPI应用主文件
├── main.py             # 项目启动入口
├── models.py           # 数据库模型定义
├── image_service.py    # 图片下载和管理服务
├── test_client.py      # API测试客户端
├── .env               # 环境配置文件
├── pyproject.toml     # 项目依赖配置
├── downloads/         # 图片下载目录
│   └── {task_id}/     # 按任务ID分组
│       └── {model_name}/  # 按模型名称分组
│           └── {prompt前10字}.jpg  # 图片文件
└── exports/           # ZIP导出目录
    └── {task_id}.zip  # 导出的ZIP文件
```

## 安装和配置

### 1. 安装依赖

```bash
pip install -e .
```

### 2. 配置数据库

编辑 `.env` 文件，配置MySQL连接信息：

```env
# MySQL数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=image_tasks

# 文件存储配置
IMAGES_BASE_PATH=./downloads
EXPORT_BASE_PATH=./exports

# FastAPI配置
API_HOST=0.0.0.0
API_PORT=8000
```

### 3. 创建数据库

确保MySQL服务运行，并创建对应的数据库：

```sql
CREATE DATABASE image_tasks CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

## 使用方法

### 启动服务

```bash
python main.py
```

服务将在 `http://localhost:8000` 启动。

### API接口

#### 1. 创建任务

**POST** `/api/tasks`

请求体格式：
```json
{
    "data": [
        {
            "model_name": "即梦",
            "data": [
                {
                    "prompt": "一只可爱的小猫",
                    "url": "https://example.com/image1.jpg"
                }
            ]
        }
    ],
    "task_id": "可选的自定义任务ID"
}
```

响应：
```json
{
    "task_id": "生成的任务ID",
    "message": "任务创建成功，图片正在后台下载"
}
```

#### 2. 获取任务信息

**GET** `/api/tasks/{task_id}`

响应：
```json
{
    "task_id": "任务ID",
    "total_images": 3,
    "models": {
        "即梦": [
            {
                "prompt": "一只可爱的小猫",
                "image_url": "https://example.com/image1.jpg",
                "created_at": "2024-01-01T12:00:00"
            }
        ]
    },
    "created_at": "2024-01-01T12:00:00"
}
```

#### 3. 导出任务

**GET** `/api/export/{task_id}`

返回ZIP文件下载。

#### 4. 手动下载图片

**POST** `/api/download/{task_id}`

手动触发图片下载（通常在创建任务时会自动下载）。

### 测试

运行测试客户端：

```bash
python test_client.py
```

## 数据库表结构

```sql
CREATE TABLE image_tasks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    task_id VARCHAR(255) NOT NULL,
    model_name VARCHAR(100) NOT NULL,
    prompt TEXT NOT NULL,
    image_url TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_task_id (task_id)
);
```

## 文件组织结构

下载的图片按以下结构组织：

```
downloads/
└── {task_id}/
    ├── 即梦/
    │   ├── 一只可爱的小猫.jpg
    │   └── 美丽的日落风.jpg
    ├── 万相/
    │   └── 科幻城市夜景.jpg
    └── 豆包/
        └── 抽象艺术作品.jpg
```

## 注意事项

1. 图片文件名使用提示词的前10个字符，非法字符会被过滤
2. 如果提示词为空或全是非法字符，会使用 `image_{id}` 作为文件名
3. 图片下载失败不会中断整个任务，会继续下载其他图片
4. ZIP导出包含完整的文件夹结构
5. 确保MySQL服务正常运行且配置正确

## 开发和扩展

- 可以通过修改 `ImageService` 类来扩展图片处理功能
- 可以通过修改 `app.py` 来添加新的API接口
- 支持添加更多的生图模型，只需在数据中指定不同的 `model_name`