"""
高级测试脚本 - 包含多种测试场景
"""
import requests
import time
import json

BASE_URL = "http://localhost:8000"

def check_server():
    """检查服务器是否运行"""
    try:
        response = requests.get(BASE_URL, timeout=5)
        return response.status_code == 200
    except:
        return False

def test_basic_workflow():
    """测试基本工作流程"""
    print("🔄 测试基本工作流程...")
    
    # 简单测试数据
    simple_data = {
        "data": [
            {
                "model_name": "测试模型",
                "data": [
                    {
                        "prompt": "测试图片",
                        "url": "https://picsum.photos/300/300?random=100"
                    }
                ]
            }
        ]
    }
    
    # 下载
    print("  📥 下载图片...")
    response = requests.post(f"{BASE_URL}/download", json=simple_data)
    if response.status_code != 200:
        print(f"  ❌ 下载失败: {response.text}")
        return False
    
    task_id = response.json()["task_id"]
    print(f"  ✅ 下载成功，任务ID: {task_id}")
    
    # 等待
    time.sleep(3)
    
    # 导出
    print("  📦 导出ZIP...")
    response = requests.get(f"{BASE_URL}/export/{task_id}")
    if response.status_code != 200:
        print(f"  ❌ 导出失败: {response.text}")
        return False
    
    print(f"  ✅ 导出成功，文件大小: {len(response.content)} bytes")
    return True

def test_multiple_models():
    """测试多模型数据"""
    print("🎯 测试多模型数据...")
    
    multi_data = {
        "data": [
            {
                "model_name": "即梦",
                "data": [
                    {"prompt": "山水画", "url": "https://picsum.photos/400/400?random=201"},
                    {"prompt": "人物肖像", "url": "https://picsum.photos/400/400?random=202"}
                ]
            },
            {
                "model_name": "万相",
                "data": [
                    {"prompt": "抽象艺术", "url": "https://picsum.photos/400/400?random=203"}
                ]
            },
            {
                "model_name": "豆包",
                "data": [
                    {"prompt": "风景摄影", "url": "https://picsum.photos/400/400?random=204"},
                    {"prompt": "建筑设计", "url": "https://picsum.photos/400/400?random=205"}
                ]
            }
        ]
    }
    
    response = requests.post(f"{BASE_URL}/download", json=multi_data)
    if response.status_code == 200:
        task_id = response.json()["task_id"]
        print(f"  ✅ 多模型下载成功: {task_id}")
        
        time.sleep(5)
        
        response = requests.get(f"{BASE_URL}/export/{task_id}")
        if response.status_code == 200:
            print(f"  ✅ 多模型导出成功: {len(response.content)} bytes")
            return True
    
    print("  ❌ 多模型测试失败")
    return False

def test_custom_task_id():
    """测试自定义任务ID"""
    print("🏷️ 测试自定义任务ID...")
    
    custom_id = "my-custom-task-123"
    data = {
        "task_id": custom_id,
        "data": [
            {
                "model_name": "自定义测试",
                "data": [
                    {"prompt": "自定义任务", "url": "https://picsum.photos/200/200?random=301"}
                ]
            }
        ]
    }
    
    response = requests.post(f"{BASE_URL}/download", json=data)
    if response.status_code == 200:
        result = response.json()
        if result["task_id"] == custom_id:
            print(f"  ✅ 自定义任务ID成功: {custom_id}")
            
            time.sleep(3)
            
            response = requests.get(f"{BASE_URL}/export/{custom_id}")
            if response.status_code == 200:
                print("  ✅ 自定义任务导出成功")
                return True
    
    print("  ❌ 自定义任务ID测试失败")
    return False

def test_error_cases():
    """测试错误情况"""
    print("⚠️ 测试错误处理...")
    
    # 测试不存在的任务ID导出
    response = requests.get(f"{BASE_URL}/export/non-existent-task")
    if response.status_code == 404:
        print("  ✅ 不存在任务ID正确返回404")
    else:
        print("  ❌ 不存在任务ID错误处理失败")
    
    # 测试空数据
    try:
        response = requests.post(f"{BASE_URL}/download", json={})
        if response.status_code != 200:
            print("  ✅ 空数据正确被拒绝")
        else:
            print("  ❌ 空数据错误处理失败")
    except:
        print("  ✅ 空数据正确被拒绝")

def test_cleanup():
    """测试清理功能"""
    print("🧹 测试清理功能...")
    
    response = requests.post(f"{BASE_URL}/cleanup")
    if response.status_code == 200:
        result = response.json()
        print(f"  ✅ 清理成功: {result['message']}")
        print(f"  📊 清理统计: {result['result']}")
    else:
        print("  ❌ 清理功能测试失败")

def main():
    """主测试函数"""
    print("🚀 开始高级测试...")
    
    # 检查服务器
    if not check_server():
        print("❌ 服务器未运行，请先启动: python app.py")
        return
    
    print("✅ 服务器运行正常\n")
    
    # 运行测试
    tests = [
        ("基本工作流程", test_basic_workflow),
        ("多模型数据", test_multiple_models),
        ("自定义任务ID", test_custom_task_id),
        ("错误处理", test_error_cases),
        ("清理功能", test_cleanup)
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        try:
            if test_func():
                passed += 1
            print()
        except Exception as e:
            print(f"  ❌ {name}测试异常: {e}\n")
    
    # 测试结果
    print("=" * 50)
    print(f"🎯 测试完成: {passed}/{total} 通过")
    if passed == total:
        print("🎉 所有测试通过！")
    else:
        print("⚠️ 部分测试失败，请检查")

if __name__ == "__main__":
    main()
