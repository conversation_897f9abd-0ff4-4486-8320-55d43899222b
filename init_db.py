"""
数据库初始化脚本
"""
import sys
from sqlalchemy import create_engine, text
from models import Base, DATABASE_URL, MYSQL_HOST, MYSQL_PORT, MYSQL_USER, MYSQL_PASSWORD, MYSQL_DATABASE
from dotenv import load_dotenv

def create_database():
    """创建数据库（如果不存在）"""
    load_dotenv()
    
    # 连接到MySQL服务器（不指定数据库）
    server_url = f"mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}"
    
    try:
        engine = create_engine(server_url)
        with engine.connect() as conn:
            # 检查数据库是否存在
            result = conn.execute(text(f"SHOW DATABASES LIKE '{MYSQL_DATABASE}'"))
            if not result.fetchone():
                # 创建数据库
                conn.execute(text(f"CREATE DATABASE {MYSQL_DATABASE} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"))
                print(f"数据库 '{MYSQL_DATABASE}' 创建成功")
            else:
                print(f"数据库 '{MYSQL_DATABASE}' 已存在")
                
    except Exception as e:
        print(f"创建数据库失败: {e}")
        return False
    
    return True

def create_tables():
    """创建数据表"""
    try:
        engine = create_engine(DATABASE_URL)
        Base.metadata.create_all(bind=engine)
        print("数据表创建成功")
        return True
    except Exception as e:
        print(f"创建数据表失败: {e}")
        return False

def init_database():
    """初始化数据库"""
    print("开始初始化数据库...")
    
    # 创建数据库
    if not create_database():
        print("数据库创建失败，请检查MySQL连接配置")
        return False
    
    # 创建数据表
    if not create_tables():
        print("数据表创建失败")
        return False
    
    print("数据库初始化完成！")
    return True

if __name__ == "__main__":
    success = init_database()
    sys.exit(0 if success else 1)
