"""
API测试脚本
"""
import requests
import time

# API基础URL
BASE_URL = "http://localhost:8000"

# 测试数据
test_data = {
    "data": [
        {
            "model_name": "即梦",
            "data": [
                {
                    "prompt": "一只可爱的小猫咪",
                    "url": "https://picsum.photos/512/512?random=1"
                },
                {
                    "prompt": "美丽的日落风景",
                    "url": "https://picsum.photos/512/512?random=2"
                }
            ]
        },
        {
            "model_name": "万相",
            "data": [
                {
                    "prompt": "科幻城市夜景",
                    "url": "https://picsum.photos/512/512?random=3"
                }
            ]
        },
        {
            "model_name": "豆包",
            "data": [
                {
                    "prompt": "蓝天白云",
                    "url": "https://picsum.photos/512/512?random=4"
                },
                {
                    "prompt": "小孩在公园玩耍",
                    "url": "https://picsum.photos/512/512?random=5"
                }
            ]
        }
    ]
}

def test_download():
    """测试下载接口"""
    print("=== 测试下载图片 ===")
    
    try:
        response = requests.post(f"{BASE_URL}/download", json=test_data)
        response.raise_for_status()
        
        result = response.json()
        print(f"✅ 下载成功: {result}")
        return result["task_id"]
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 下载失败: {e}")
        return None

def test_export(task_id):
    """测试导出接口"""
    print(f"\n=== 测试导出ZIP: {task_id} ===")
    
    try:
        response = requests.get(f"{BASE_URL}/export/{task_id}")
        response.raise_for_status()
        
        # 保存ZIP文件
        filename = f"test_export_{task_id}.zip"
        with open(filename, 'wb') as f:
            f.write(response.content)
        
        print(f"✅ 导出成功: {filename}")
        print(f"📦 文件大小: {len(response.content)} bytes")
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 导出失败: {e}")

def test_api_info():
    """测试API信息接口"""
    print("=== 测试API信息 ===")
    
    try:
        response = requests.get(BASE_URL)
        response.raise_for_status()
        
        result = response.json()
        print(f"📋 API信息: {result['message']}")
        print("🔗 可用接口:")
        for name, desc in result['endpoints'].items():
            print(f"   • {name}: {desc}")
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 获取API信息失败: {e}")

def main():
    """主测试函数"""
    print("🚀 开始测试图片管理系统...")
    
    # 1. 测试API信息
    test_api_info()
    
    # 2. 测试下载图片
    task_id = test_download()
    if not task_id:
        print("❌ 下载失败，停止测试")
        return
    
    # 3. 等待下载完成
    print("\n⏳ 等待图片下载完成...")
    time.sleep(5)
    
    # 4. 测试导出ZIP
    test_export(task_id)
    
    print("\n🎉 测试完成！")
    print(f"📁 任务ID: {task_id}")
    print("💡 提示: 可以检查 downloads/ 和 exports/ 文件夹查看生成的文件")

if __name__ == "__main__":
    main()
