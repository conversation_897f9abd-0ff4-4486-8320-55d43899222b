"""
简单测试脚本
"""
import requests

# 测试数据
test_data = {
    "data": [
        {
            "model_name": "即梦",
            "data": [
                {
                    "prompt": "一只可爱的小猫咪",
                    "url": "https://picsum.photos/512/512?random=1"
                },
                {
                    "prompt": "美丽的日落风景",
                    "url": "https://picsum.photos/512/512?random=2"
                }
            ]
        },
        {
            "model_name": "万相",
            "data": [
                {
                    "prompt": "科幻城市夜景",
                    "url": "https://picsum.photos/512/512?random=3"
                }
            ]
        },
        {
            "model_name": "豆包",
            "data": [
                {
                    "prompt": "抽象艺术作品",
                    "url": "https://picsum.photos/512/512?random=4"
                }
            ]
        }
    ]
}

def test_export():
    """测试导出接口"""
    print("开始测试图片导出...")
    
    try:
        response = requests.post("http://localhost:8000/export", json=test_data)
        response.raise_for_status()
        
        # 保存ZIP文件
        filename = "exported_images.zip"
        with open(filename, 'wb') as f:
            f.write(response.content)
        
        print(f"导出成功！文件保存为: {filename}")
        print(f"文件大小: {len(response.content)} bytes")
        
    except requests.exceptions.RequestException as e:
        print(f"导出失败: {e}")

if __name__ == "__main__":
    test_export()
