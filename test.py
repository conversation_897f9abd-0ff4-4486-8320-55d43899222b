"""
测试脚本
"""
import requests
import time

# 测试数据
test_data = {
    "data": [
        {
            "model_name": "即梦",
            "data": [
                {
                    "prompt": "蓝天白云",
                    "url": "https://p9-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/202508291043136291BEA5D763EBA36B0A-6893-0~tplv-vuqhorh59i-image-v1.image?rk3s=7f9e702d&x-expires=1756521800&x-signature=ntGHR5O55unJlOI3ZHG%2BX8DBdx8%3D"
                },
                {
                    "prompt": "小孩在公园玩耍",
                    "url": "https://p26-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/20250829104321EC195294A5B2BCA71092-1298-0~tplv-vuqhorh59i-image-v1.image?rk3s=7f9e702d&x-expires=1756521808&x-signature=ZUNkiJVNOQBT2L8dCr3req7c3js%3D"
                }
            ]
        },
{
            "model_name": "豆包",
            "data": [
                {
                    "prompt": "蓝天白云",
                    "url": "https://p9-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/202508291043136291BEA5D763EBA36B0A-6893-0~tplv-vuqhorh59i-image-v1.image?rk3s=7f9e702d&x-expires=1756521800&x-signature=ntGHR5O55unJlOI3ZHG%2BX8DBdx8%3D"
                },
                {
                    "prompt": "小孩在公园玩耍",
                    "url": "https://p26-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/20250829104321EC195294A5B2BCA71092-1298-0~tplv-vuqhorh59i-image-v1.image?rk3s=7f9e702d&x-expires=1756521808&x-signature=ZUNkiJVNOQBT2L8dCr3req7c3js%3D"
                }
            ]
        }
    ]
}

def test_download():
    """测试下载接口"""
    print("=== 测试下载图片 ===")

    try:
        response = requests.post("http://localhost:8000/download", json=test_data)
        response.raise_for_status()

        result = response.json()
        print(f"下载结果: {result}")
        return result["task_id"]

    except requests.exceptions.RequestException as e:
        print(f"下载失败: {e}")
        return None

def test_export(task_id):
    """测试导出接口"""
    print(f"\n=== 测试导出ZIP: {task_id} ===")

    try:
        response = requests.get(f"http://localhost:8000/export/{task_id}")
        response.raise_for_status()

        # 保存ZIP文件
        filename = f"exported_{task_id}.zip"
        with open(filename, 'wb') as f:
            f.write(response.content)

        print(f"导出成功！文件保存为: {filename}")
        print(f"文件大小: {len(response.content)} bytes")

    except requests.exceptions.RequestException as e:
        print(f"导出失败: {e}")

def test_cleanup():
    """测试清理接口"""
    print("\n=== 测试手动清理 ===")

    try:
        response = requests.post("http://localhost:8000/cleanup")
        response.raise_for_status()

        result = response.json()
        print(f"清理结果: {result}")

    except requests.exceptions.RequestException as e:
        print(f"清理失败: {e}")

def main():
    """主测试函数"""
    print("开始测试图片管理系统...")

    # 1. 下载图片
    task_id = test_download()
    if not task_id:
        return

    # 2. 等待下载完成
    print("\n等待图片下载完成...")
    time.sleep(3)

    # 3. 导出ZIP
    test_export(task_id)

    # 4. 测试清理功能
    test_cleanup()

    print("\n测试完成！")

if __name__ == "__main__":
    main()

"""
    "fastapi>=0.104.0",
    "uvicorn>=0.24.0",
    "sqlalchemy>=2.0.0",
    "pymysql>=1.1.0",
    "python-dotenv>=1.0.0",
    "requests>=2.31.0",
    "aiofiles>=23.2.0",
    "python-multipart>=0.0.6"

"""