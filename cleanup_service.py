"""
定时清理服务
"""
import schedule
import time
import threading
import os
from models import cleanup_old_data
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()


class CleanupService:
    def __init__(self):
        self.running = False
        self.thread = None
        self.cleanup_time = os.getenv("CLEANUP_TIME", "23:00")

    def start_scheduler(self):
        """启动定时任务调度器"""
        if self.running:
            print("清理服务已经在运行中")
            return

        # 根据配置设置清理时间
        schedule.every().day.at(self.cleanup_time).do(self.run_cleanup)

        # 也可以设置每小时检查一次（用于测试）
        # schedule.every().hour.do(self.run_cleanup)

        self.running = True
        self.thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.thread.start()

        print(f"清理服务已启动，将在每天{self.cleanup_time}执行清理任务")

    def stop_scheduler(self):
        """停止定时任务调度器"""
        self.running = False
        schedule.clear()
        print("清理服务已停止")

    def _run_scheduler(self):
        """运行调度器的内部方法"""
        while self.running:
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次

    def run_cleanup(self):
        """执行清理任务"""
        try:
            print(f"[{datetime.now()}] 开始执行定时清理任务...")
            result = cleanup_old_data()
            print(f"[{datetime.now()}] 清理任务完成: {result}")
        except Exception as e:
            print(f"[{datetime.now()}] 清理任务失败: {e}")

    def manual_cleanup(self):
        """手动执行清理任务"""
        print("手动执行清理任务...")
        return self.run_cleanup()


# 全局清理服务实例
cleanup_service = CleanupService()
